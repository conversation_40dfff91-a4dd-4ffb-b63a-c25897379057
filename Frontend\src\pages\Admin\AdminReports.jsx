import React, { useState } from "react";
import { useSelector } from "react-redux";
import { selectAnalytics, selectStats } from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminReports.css";

// Icons
import { FaChartLine, FaUsers, FaDollarSign, FaDownload, FaCalendarAlt } from "react-icons/fa";
import { MdTrendingUp, MdTrendingDown } from "react-icons/md";

const AdminReports = () => {
  const analytics = useSelector(selectAnalytics);
  const stats = useSelector(selectStats);
  const [selectedPeriod, setSelectedPeriod] = useState("6months");

  // Mock chart data - in real app, this would come from API
  const chartData = {
    salesChart: {
      labels: analytics.salesChart.labels,
      datasets: [
        {
          label: 'Sales Revenue',
          data: analytics.salesChart.data,
          borderColor: '#ee3425',
          backgroundColor: 'rgba(238, 52, 37, 0.1)',
          tension: 0.4,
        }
      ]
    },
    userRegistrations: {
      labels: analytics.userRegistrations.labels,
      datasets: [
        {
          label: 'New Users',
          data: analytics.userRegistrations.data,
          backgroundColor: '#3b82f6',
        }
      ]
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Calculate percentage change (mock data)
  const getPercentageChange = (current, previous) => {
    const change = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(change).toFixed(1),
      isPositive: change > 0
    };
  };

  // Mock previous period data
  const previousStats = {
    totalRevenue: 10200,
    totalUsers: 1200,
    totalContent: 420,
    monthlyRevenue: 2800
  };

  const revenueChange = getPercentageChange(stats.totalRevenue, previousStats.totalRevenue);
  const usersChange = getPercentageChange(stats.totalBuyers + stats.totalSellers, previousStats.totalUsers);
  const contentChange = getPercentageChange(stats.totalContent, previousStats.totalContent);
  const monthlyRevenueChange = getPercentageChange(stats.monthlyRevenue, previousStats.monthlyRevenue);

  return (
    <AdminLayout>
      <div className="AdminReports">
        {/* Header with Export Options */}
        <div className="AdminReports__header">
          <div className="header-left">
            <div className="period-selector">
              <FaCalendarAlt className="calendar-icon" />
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="period-select"
              >
                <option value="1month">Last Month</option>
                <option value="3months">Last 3 Months</option>
                <option value="6months">Last 6 Months</option>
                <option value="1year">Last Year</option>
              </select>
            </div>
          </div>
          
          <div className="header-right">
            <button className="btn btn-outline">
              <FaDownload />
              Export PDF
            </button>
            <button className="btn btn-primary">
              <FaDownload />
              Export CSV
            </button>
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="AdminReports__metrics">
          <div className="metric-card revenue">
            <div className="metric-header">
              <div className="metric-icon">
                <FaDollarSign />
              </div>
              <div className={`metric-trend ${revenueChange.isPositive ? 'positive' : 'negative'}`}>
                {revenueChange.isPositive ? <MdTrendingUp /> : <MdTrendingDown />}
                {revenueChange.value}%
              </div>
            </div>
            <div className="metric-content">
              <div className="metric-number">{formatCurrency(stats.totalRevenue)}</div>
              <div className="metric-label">Total Revenue</div>
              <div className="metric-sublabel">vs previous period</div>
            </div>
          </div>

          <div className="metric-card users">
            <div className="metric-header">
              <div className="metric-icon">
                <FaUsers />
              </div>
              <div className={`metric-trend ${usersChange.isPositive ? 'positive' : 'negative'}`}>
                {usersChange.isPositive ? <MdTrendingUp /> : <MdTrendingDown />}
                {usersChange.value}%
              </div>
            </div>
            <div className="metric-content">
              <div className="metric-number">{(stats.totalBuyers + stats.totalSellers).toLocaleString()}</div>
              <div className="metric-label">Total Users</div>
              <div className="metric-sublabel">vs previous period</div>
            </div>
          </div>

          <div className="metric-card content">
            <div className="metric-header">
              <div className="metric-icon">
                <FaChartLine />
              </div>
              <div className={`metric-trend ${contentChange.isPositive ? 'positive' : 'negative'}`}>
                {contentChange.isPositive ? <MdTrendingUp /> : <MdTrendingDown />}
                {contentChange.value}%
              </div>
            </div>
            <div className="metric-content">
              <div className="metric-number">{stats.totalContent.toLocaleString()}</div>
              <div className="metric-label">Total Content</div>
              <div className="metric-sublabel">vs previous period</div>
            </div>
          </div>

          <div className="metric-card monthly">
            <div className="metric-header">
              <div className="metric-icon">
                <FaDollarSign />
              </div>
              <div className={`metric-trend ${monthlyRevenueChange.isPositive ? 'positive' : 'negative'}`}>
                {monthlyRevenueChange.isPositive ? <MdTrendingUp /> : <MdTrendingDown />}
                {monthlyRevenueChange.value}%
              </div>
            </div>
            <div className="metric-content">
              <div className="metric-number">{formatCurrency(stats.monthlyRevenue)}</div>
              <div className="metric-label">Monthly Revenue</div>
              <div className="metric-sublabel">vs previous month</div>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="AdminReports__charts">
          {/* Sales Over Time Chart */}
          <div className="chart-container">
            <div className="chart-header">
              <h3>Sales Over Time</h3>
              <p>Revenue trends for the selected period</p>
            </div>
            <div className="chart-placeholder">
              <div className="chart-mock">
                <div className="chart-bars">
                  {analytics.salesChart.data.map((value, index) => (
                    <div
                      key={index}
                      className="chart-bar"
                      style={{ height: `${(value / Math.max(...analytics.salesChart.data)) * 100}%` }}
                    >
                      <span className="bar-value">{formatCurrency(value)}</span>
                    </div>
                  ))}
                </div>
                <div className="chart-labels">
                  {analytics.salesChart.labels.map((label, index) => (
                    <span key={index} className="chart-label">{label}</span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* User Registrations Chart */}
          <div className="chart-container">
            <div className="chart-header">
              <h3>User Registrations</h3>
              <p>Monthly user growth</p>
            </div>
            <div className="chart-placeholder">
              <div className="chart-mock">
                <div className="chart-bars">
                  {analytics.userRegistrations.data.map((value, index) => (
                    <div
                      key={index}
                      className="chart-bar users-bar"
                      style={{ height: `${(value / Math.max(...analytics.userRegistrations.data)) * 100}%` }}
                    >
                      <span className="bar-value">{value}</span>
                    </div>
                  ))}
                </div>
                <div className="chart-labels">
                  {analytics.userRegistrations.labels.map((label, index) => (
                    <span key={index} className="chart-label">{label}</span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Distribution Charts */}
        <div className="AdminReports__distributions">
          {/* Content Categories Distribution */}
          <div className="distribution-container">
            <div className="distribution-header">
              <h3>Content Categories Distribution</h3>
              <p>Breakdown by content type</p>
            </div>
            <div className="distribution-chart">
              {analytics.categoryDistribution.labels.map((label, index) => {
                const percentage = analytics.categoryDistribution.data[index];
                return (
                  <div key={index} className="distribution-item">
                    <div className="distribution-bar">
                      <div
                        className="distribution-fill"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <div className="distribution-info">
                      <span className="distribution-label">{label}</span>
                      <span className="distribution-value">{percentage}%</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Revenue by Category */}
          <div className="distribution-container">
            <div className="distribution-header">
              <h3>Revenue by Category</h3>
              <p>Revenue breakdown by content type</p>
            </div>
            <div className="distribution-chart">
              {analytics.revenueByCategory.labels.map((label, index) => {
                const amount = analytics.revenueByCategory.data[index];
                const maxAmount = Math.max(...analytics.revenueByCategory.data);
                const percentage = (amount / maxAmount) * 100;
                return (
                  <div key={index} className="distribution-item">
                    <div className="distribution-bar">
                      <div
                        className="distribution-fill revenue-fill"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <div className="distribution-info">
                      <span className="distribution-label">{label}</span>
                      <span className="distribution-value">{formatCurrency(amount)}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminReports;
