import React, { useState } from "react";
import { useSelector } from "react-redux";
import { selectCMSPages } from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminCMSPages.css";

// Icons
import { FaFileAlt, FaSearch, FaEdit, FaTrash, FaEye, FaToggleOn, FaToggleOff } from "react-icons/fa";
import { MdAdd, MdPublish } from "react-icons/md";

const AdminCMSPages = () => {
  const cmsPages = useSelector(selectCMSPages);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedPages, setSelectedPages] = useState([]);

  // Filter pages based on search and filters
  const filteredPages = cmsPages.filter(page => {
    const matchesSearch =
      page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.slug.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || page.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedPages(filteredPages.map(page => page.id));
    } else {
      setSelectedPages([]);
    }
  };

  // Handle individual select
  const handleSelectPage = (pageId) => {
    setSelectedPages(prev =>
      prev.includes(pageId)
        ? prev.filter(id => id !== pageId)
        : [...prev, pageId]
    );
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case 'published':
        return 'status-badge published';
      case 'draft':
        return 'status-badge draft';
      default:
        return 'status-badge';
    }
  };

  return (
    <AdminLayout>
      <div className="AdminCMSPages">
        {/* Header Actions */}
        <div className="AdminCMSPages__header">
          <div className="header-left">
            <div className="search-container">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search pages by title or slug..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>

          <div className="header-right">
            <button className="btn btn-primary">
              <MdAdd />
              Create New Page
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="AdminCMSPages__filters">
          <div className="filter-group">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
            </select>
          </div>

          {selectedPages.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedPages.length} selected
              </span>
              <button className="btn btn-success">
                <MdPublish />
                Publish
              </button>
              <button className="btn btn-outline">Unpublish</button>
              <button className="btn btn-danger">Delete</button>
            </div>
          )}
        </div>

        {/* Pages Table */}
        <div className="AdminCMSPages__table">
          <div className="table-container">
            <table className="pages-table">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedPages.length === filteredPages.length && filteredPages.length > 0}
                    />
                  </th>
                  <th>Page Title</th>
                  <th>URL Slug</th>
                  <th>Status</th>
                  <th>Last Modified</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPages.map((page) => (
                  <tr key={page.id}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedPages.includes(page.id)}
                        onChange={() => handleSelectPage(page.id)}
                      />
                    </td>
                    <td>
                      <div className="page-info">
                        <div className="page-icon">
                          <FaFileAlt />
                        </div>
                        <div className="page-details">
                          <span className="page-title">{page.title}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span className="page-slug">/{page.slug}</span>
                    </td>
                    <td>
                      <div className="status-toggle">
                        <span className={getStatusBadge(page.status)}>
                          {page.status}
                        </span>
                        <button className="toggle-btn">
                          {page.status === 'published' ? (
                            <FaToggleOn className="toggle-on" />
                          ) : (
                            <FaToggleOff className="toggle-off" />
                          )}
                        </button>
                      </div>
                    </td>
                    <td>{formatDate(page.lastModified)}</td>
                    <td>
                      <div className="table-actions">
                        <button className="btn-action view" title="View Page">
                          <FaEye />
                        </button>
                        <button className="btn-action edit" title="Edit Page">
                          <FaEdit />
                        </button>
                        <button className="btn-action delete" title="Delete Page">
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredPages.length === 0 && (
            <div className="no-results">
              <FaFileAlt className="no-results-icon" />
              <h3>No pages found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="AdminCMSPages__quick-actions">
          <h3>Quick Actions</h3>
          <div className="quick-actions-grid">
            <div className="quick-action-card">
              <div className="action-icon">
                <MdAdd />
              </div>
              <div className="action-content">
                <h4>Create New Page</h4>
                <p>Add a new CMS page to your website</p>
                <button className="btn btn-outline">Create Page</button>
              </div>
            </div>

            <div className="quick-action-card">
              <div className="action-icon">
                <FaEdit />
              </div>
              <div className="action-content">
                <h4>Edit Templates</h4>
                <p>Customize page templates and layouts</p>
                <button className="btn btn-outline">Edit Templates</button>
              </div>
            </div>

            <div className="quick-action-card">
              <div className="action-icon">
                <MdPublish />
              </div>
              <div className="action-content">
                <h4>Bulk Publish</h4>
                <p>Publish multiple draft pages at once</p>
                <button className="btn btn-outline">Bulk Publish</button>
              </div>
            </div>
          </div>
        </div>

        {/* Pagination */}
        <div className="AdminCMSPages__pagination">
          <div className="pagination-info">
            Showing {filteredPages.length} of {cmsPages.length} pages
          </div>
          <div className="pagination-controls">
            <button className="btn btn-outline" disabled>Previous</button>
            <span className="page-number active">1</span>
            <button className="btn btn-outline" disabled>Next</button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminCMSPages;
