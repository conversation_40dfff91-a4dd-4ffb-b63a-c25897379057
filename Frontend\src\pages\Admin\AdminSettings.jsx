import React, { useState } from "react";
import { useSelector } from "react-redux";
import { selectProfile } from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import FinancialSettings from "../../components/admin/FinancialSettings";
import "../../styles/AdminSettings.css";

// Icons
import { <PERSON>a<PERSON>og, FaUser, FaBell, FaLock, FaUpload, FaSave } from "react-icons/fa";
import { MdSecurity, MdNotifications, MdSettings } from "react-icons/md";

const AdminSettings = () => {
  const profile = useSelector(selectProfile);
  const [activeTab, setActiveTab] = useState("general");
  const [settings, setSettings] = useState({
    siteName: "XOSportsHub",
    siteDescription: "Premier sports content marketplace",
    contactEmail: "<EMAIL>",
    supportEmail: "<EMAIL>",
    logoUrl: "",
    emailNotifications: true,
    pushNotifications: false,
    marketingEmails: true,
    securityAlerts: true,
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
  });

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    // Handle save logic here
    console.log("Saving settings:", settings);
  };

  const tabs = [
    { id: "general", label: "General Settings", icon: <MdSettings /> },
    { id: "financial", label: "Financial", icon: <FaCog /> },
    { id: "notifications", label: "Notifications", icon: <MdNotifications /> },
    { id: "security", label: "Security", icon: <MdSecurity /> },
    { id: "roles", label: "User Roles", icon: <FaLock /> },
  ];

  return (
    <AdminLayout>
      <div className="AdminSettings">
        {/* Settings Navigation */}
        <div className="AdminSettings__nav">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Settings Content */}
        <div className="AdminSettings__content">
          {activeTab === "general" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>General Settings</h3>
                <p>Configure basic site settings and information</p>
              </div>

              <div className="settings-form">
                <div className="form-group">
                  <label>Site Name</label>
                  <input
                    type="text"
                    value={settings.siteName}
                    onChange={(e) => handleInputChange("siteName", e.target.value)}
                    className="form-input"
                  />
                </div>

                <div className="form-group">
                  <label>Site Description</label>
                  <textarea
                    value={settings.siteDescription}
                    onChange={(e) => handleInputChange("siteDescription", e.target.value)}
                    className="form-textarea"
                    rows="3"
                  />
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Contact Email</label>
                    <input
                      type="email"
                      value={settings.contactEmail}
                      onChange={(e) => handleInputChange("contactEmail", e.target.value)}
                      className="form-input"
                    />
                  </div>

                  <div className="form-group">
                    <label>Support Email</label>
                    <input
                      type="email"
                      value={settings.supportEmail}
                      onChange={(e) => handleInputChange("supportEmail", e.target.value)}
                      className="form-input"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>Site Logo</label>
                  <div className="logo-upload">
                    <div className="logo-preview">
                      {settings.logoUrl ? (
                        <img src={settings.logoUrl} alt="Site Logo" />
                      ) : (
                        <div className="logo-placeholder">
                          <FaUpload />
                          <span>No logo uploaded</span>
                        </div>
                      )}
                    </div>
                    <button className="btn btn-outline">
                      <FaUpload />
                      Upload Logo
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "financial" && (
            <FinancialSettings />
          )}

          {activeTab === "notifications" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>Notification Settings</h3>
                <p>Configure email and push notification preferences</p>
              </div>

              <div className="settings-form">
                <div className="notification-group">
                  <h4>Email Notifications</h4>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Email Notifications</span>
                      <span className="toggle-description">Receive general email notifications</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={settings.emailNotifications}
                        onChange={(e) => handleInputChange("emailNotifications", e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Marketing Emails</span>
                      <span className="toggle-description">Receive marketing and promotional emails</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={settings.marketingEmails}
                        onChange={(e) => handleInputChange("marketingEmails", e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Security Alerts</span>
                      <span className="toggle-description">Receive security-related notifications</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={settings.securityAlerts}
                        onChange={(e) => handleInputChange("securityAlerts", e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                </div>

                <div className="notification-group">
                  <h4>Push Notifications</h4>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Browser Notifications</span>
                      <span className="toggle-description">Receive push notifications in your browser</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={settings.pushNotifications}
                        onChange={(e) => handleInputChange("pushNotifications", e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "security" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>Security Settings</h3>
                <p>Configure security and authentication settings</p>
              </div>

              <div className="settings-form">
                <div className="security-group">
                  <h4>Authentication</h4>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Two-Factor Authentication</span>
                      <span className="toggle-description">Require 2FA for admin accounts</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={settings.twoFactorAuth}
                        onChange={(e) => handleInputChange("twoFactorAuth", e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>

                  <div className="form-group">
                    <label>Session Timeout (minutes)</label>
                    <input
                      type="number"
                      value={settings.sessionTimeout}
                      onChange={(e) => handleInputChange("sessionTimeout", parseInt(e.target.value))}
                      className="form-input"
                      min="5"
                      max="480"
                    />
                  </div>

                  <div className="form-group">
                    <label>Password Expiry (days)</label>
                    <input
                      type="number"
                      value={settings.passwordExpiry}
                      onChange={(e) => handleInputChange("passwordExpiry", parseInt(e.target.value))}
                      className="form-input"
                      min="30"
                      max="365"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "roles" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>User Roles & Permissions</h3>
                <p>Manage user roles and their permissions</p>
              </div>

              <div className="roles-grid">
                <div className="role-card">
                  <div className="role-header">
                    <h4>Admin</h4>
                    <span className="role-count">2 users</span>
                  </div>
                  <div className="role-permissions">
                    <span className="permission-tag">Full Access</span>
                    <span className="permission-tag">User Management</span>
                    <span className="permission-tag">Content Management</span>
                    <span className="permission-tag">System Settings</span>
                  </div>
                  <button className="btn btn-outline">Edit Permissions</button>
                </div>

                <div className="role-card">
                  <div className="role-header">
                    <h4>Seller</h4>
                    <span className="role-count">89 users</span>
                  </div>
                  <div className="role-permissions">
                    <span className="permission-tag">Content Upload</span>
                    <span className="permission-tag">Sales Management</span>
                    <span className="permission-tag">Profile Management</span>
                  </div>
                  <button className="btn btn-outline">Edit Permissions</button>
                </div>

                <div className="role-card">
                  <div className="role-header">
                    <h4>Buyer</h4>
                    <span className="role-count">1,247 users</span>
                  </div>
                  <div className="role-permissions">
                    <span className="permission-tag">Content Purchase</span>
                    <span className="permission-tag">Profile Management</span>
                    <span className="permission-tag">Order History</span>
                  </div>
                  <button className="btn btn-outline">Edit Permissions</button>
                </div>
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="settings-actions">
            <button className="btn btn-primary" onClick={handleSave}>
              <FaSave />
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
