/* AdminSidebar Component Styles */
.AdminSidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  width: 100%;
  height: 100%;
}

.AdminSidebar__container {
  display: flex;
  flex-direction: column;
  padding: var(--basefont);
  height: 100%;
}

/* Logo Section */
.AdminSidebar__logo {
  text-align: center;
  padding: var(--heading6) 0;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: var(--heading6);
}

.AdminSidebar__logo h3 {
  color: var(--primary-color);
  font-size: var(--heading5);
  font-weight: 700;
  margin: 0;
}

.AdminSidebar__logo span {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  font-weight: 500;
}

/* Navigation Menu */
.AdminSidebar__menu {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  gap: 4px;
}

.AdminSidebar__item {
  display: flex;
  align-items: center;
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: var(--basefont);
}

.AdminSidebar__item:hover {
  background-color: var(--bg-gray);
  color: var(--btn-color);
}

.AdminSidebar__item.active {
  background-color: var(--btn-color);
  color: var(--white);
  font-weight: 600;
}

.AdminSidebar__icon {
  margin-right: var(--smallfont);
  font-size: var(--heading6);
  min-width: 20px;
}

/* Logout Section */
.AdminSidebar__logout {
  margin-top: auto;
  border-top: 1px solid var(--light-gray);
  padding-top: var(--basefont);
}

.AdminSidebar__logout .logout-item {
  color: var(--btn-color);
  font-weight: 600;
}

.AdminSidebar__logout .logout-item:hover {
  background-color: rgba(238, 52, 37, 0.1);
  color: var(--btn-color);
}

/* Responsive styles */
@media (max-width: 768px) {
  .AdminSidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-index-modal);
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
  }

  .AdminSidebar.open {
    display: flex;
  }

  .AdminSidebar__container {
    width: 80%;
    max-width: 300px;
    height: 100%;
    background-color: var(--white);
    overflow-y: auto;
  }

  .AdminSidebar__logo h3 {
    font-size: var(--heading6);
  }

  .AdminSidebar__item {
    padding: var(--basefont);
  }

  .AdminSidebar__item span {
    font-size: var(--smallfont);
  }
}

/* Collapsible sidebar for desktop */
.AdminSidebar.collapsed {
  width: 60px;
}

.AdminSidebar.collapsed .AdminSidebar__logo span,
.AdminSidebar.collapsed .AdminSidebar__item span {
  display: none;
}

.AdminSidebar.collapsed .AdminSidebar__logo h3 {
  font-size: var(--smallfont);
}

.AdminSidebar.collapsed .AdminSidebar__item {
  justify-content: center;
  padding: var(--smallfont);
}

.AdminSidebar.collapsed .AdminSidebar__icon {
  margin-right: 0;
}

/* Smooth transitions for collapse */
.AdminSidebar {
  transition: width 0.3s ease;
}

.AdminSidebar__logo span,
.AdminSidebar__item span {
  transition: opacity 0.3s ease;
}

.AdminSidebar.collapsed .AdminSidebar__logo span,
.AdminSidebar.collapsed .AdminSidebar__item span {
  opacity: 0;
}

/* Active state enhancements */
.AdminSidebar__item.active .AdminSidebar__icon {
  transform: scale(1.1);
}

/* Hover effects */
.AdminSidebar__item:not(.active):hover .AdminSidebar__icon {
  transform: scale(1.05);
}

/* Focus states for accessibility */
.AdminSidebar__item:focus {
  outline: 2px solid var(--btn-color);
  outline-offset: 2px;
}

/* Custom scrollbar for mobile */
@media (max-width: 768px) {
  .AdminSidebar__container::-webkit-scrollbar {
    width: 4px;
  }

  .AdminSidebar__container::-webkit-scrollbar-track {
    background: var(--bg-gray);
  }

  .AdminSidebar__container::-webkit-scrollbar-thumb {
    background: var(--light-gray);
    border-radius: 2px;
  }

  .AdminSidebar__container::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
  }
}
